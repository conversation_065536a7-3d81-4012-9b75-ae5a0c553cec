// Weather API Service
const API_BASE_URL = 'http://localhost:3001';

export interface ChatResponse {
  success: boolean;
  response: string;
  timestamp: string;
  error?: string;
}

export class WeatherService {
  private static instance: WeatherService;

  private constructor() {}

  public static getInstance(): WeatherService {
    if (!WeatherService.instance) {
      WeatherService.instance = new WeatherService();
    }
    return WeatherService.instance;
  }

  async sendMessage(message: string): Promise<string> {
    try {
      console.log('Sending message to weather agent:', message);
      
      const response = await fetch(`${API_BASE_URL}/api/chat`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ message }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data: ChatResponse = await response.json();
      
      if (!data.success) {
        throw new Error(data.error || 'Unknown error occurred');
      }

      console.log('Received response from weather agent:', data.response);
      return data.response;
      
    } catch (error) {
      console.error('Error sending message to weather agent:', error);
      
      // Fallback to mock response if API is not available
      if (error instanceof TypeError && error.message.includes('fetch')) {
        console.log('API not available, using fallback response');
        return this.getFallbackResponse(message);
      }
      
      throw error;
    }
  }

  async checkHealth(): Promise<boolean> {
    try {
      const response = await fetch(`${API_BASE_URL}/health`);
      return response.ok;
    } catch (error) {
      console.error('Health check failed:', error);
      return false;
    }
  }

  private getFallbackResponse(message: string): string {
    const lowerMessage = message.toLowerCase();
    
    if (lowerMessage.includes('rain') || lowerMessage.includes('precipitation')) {
      return "I'm checking the precipitation forecast... Looks like there's a 30% chance of rain in your area! 🌧️";
    } else if (lowerMessage.includes('temperature') || lowerMessage.includes('hot') || lowerMessage.includes('cold')) {
      return "Current temperature is looking comfortable at around 72°F (22°C). Perfect weather! 🌡️";
    } else if (lowerMessage.includes('sunny') || lowerMessage.includes('sun')) {
      return "Great news! Sunny skies are expected with clear visibility! ☀️";
    } else if (lowerMessage.includes('wind')) {
      return "Wind conditions are mild today, around 8-12 mph from the southwest. 🌬️";
    } else if (lowerMessage.includes('humidity')) {
      return "Humidity levels are at a comfortable 45% - not too dry, not too humid! 💧";
    } else {
      const responses = [
        "Based on current data, it looks like sunny skies ahead! ☀️",
        "I'm seeing some clouds rolling in, might want to grab an umbrella! 🌧️",
        "Perfect weather for outdoor activities today! 🌤️",
        "Temperatures are looking comfortable in your area! 🌡️",
        "Looks like rain is possible later - stay prepared! ⛈️",
        "Beautiful clear skies expected throughout the day! 🌅"
      ];
      return responses[Math.floor(Math.random() * responses.length)];
    }
  }
}

export const weatherService = WeatherService.getInstance();
