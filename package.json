{"name": "weather-forecast-mcp-with-smithery", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "<PERSON>ra dev", "build": "mastra build"}, "keywords": [], "author": "", "license": "ISC", "type": "module", "engines": {"node": ">=20.9.0"}, "dependencies": {"@ai-sdk/google": "^1.2.18", "@mastra/core": "^0.10.0", "@mastra/libsql": "^0.10.0", "@mastra/memory": "^0.10.0", "@modelcontextprotocol/sdk": "^1.12.0", "zod": "^3.25.30"}, "devDependencies": {"@types/node": "^22.15.21", "mastra": "^0.10.0", "typescript": "^5.8.3"}}