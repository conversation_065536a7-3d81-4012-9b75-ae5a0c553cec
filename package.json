{"name": "weather-forecast-mcp-with-smithery", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "<PERSON>ra dev", "build": "mastra build", "server": "tsx src/server/index.ts", "server:dev": "tsx watch src/server/index.ts"}, "keywords": [], "author": "", "license": "ISC", "type": "module", "engines": {"node": ">=20.9.0"}, "dependencies": {"@ai-sdk/google": "^1.2.18", "@mastra/core": "^0.10.0", "@mastra/libsql": "^0.10.0", "@mastra/memory": "^0.10.0", "@modelcontextprotocol/sdk": "^1.12.0", "@types/cors": "^2.8.18", "@types/express": "^5.0.2", "cors": "^2.8.5", "express": "^5.1.0", "zod": "^3.25.30"}, "devDependencies": {"@types/node": "^22.15.21", "mastra": "^0.10.0", "tsx": "^4.19.4", "typescript": "^5.8.3"}}