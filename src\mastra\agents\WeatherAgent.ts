import { Agent } from "@mastra/core/agent";
import { google } from "@ai-sdk/google";
import { Memory } from "@mastra/memory";
import { LibSQLStore } from "@mastra/libsql";
import { mcp } from "../mcp/weatherMcp";


const memory = new Memory({
    storage: new LibSQLStore({
        url: "file:../../memory.db",
    }),
});



// Agent'ı async fonksiyon içinde oluştur
export const createWeatherAgent = async () => {
    const tools = await mcp.getTools();

    return new Agent({
        name: "Weather Agent With MCP",
        instructions: "You are a weather agent but you can use mcp for weather.",
        model: google("gemini-2.0-flash"),
        memory: memory,
        tools: tools // MCP istemcisini agent'a ekle
    });
};

// // Geçici olarak tools olmadan agent oluştur
export const weatherAgent = new Agent({
    name: "Weather Agent With MCP",
    instructions: "You are a weather agent but you can use mcp for weather.",
    model: google("gemini-2.0-flash"),
    memory: memory
    // tools kısmını kaldırdık
});
